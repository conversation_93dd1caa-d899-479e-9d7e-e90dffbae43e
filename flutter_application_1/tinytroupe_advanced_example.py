#!/usr/bin/env python3
"""
TinyTroupe 高级使用示例
演示市场研究、产品测试、团队协作等复杂场景
"""

import os
import json
from tinytroupe.agent import TinyPerson
from tinytroupe.environment import TinyWorld
from tinytroupe.factory import TinyPersonFactory
from tinytroupe.validation import TinyPersonValidator
from tinytroupe.extraction import ResultsExtractor, ResultsReducer
import tinytroupe.control as control

class ProductResearchSimulation:
    """产品研究仿真类"""
    
    def __init__(self):
        self.agents = []
        self.world = None
        self.results = []
    
    def create_research_team(self):
        """创建研究团队"""
        print("👥 创建产品研究团队...")
        
        # 从JSON文件加载开发者
        developer = TinyPerson.load_specification("chinese_developer.agent.json")
        
        # 创建产品经理
        pm = TinyPerson("李雅")
        pm.define("age", 30)
        pm.define("occupation", {
            "title": "产品经理",
            "organization": "腾讯科技",
            "description": "负责移动应用产品规划和用户体验设计"
        })
        pm.define("personality", {
            "traits": [
                "用户导向思维，关注用户需求",
                "数据驱动决策，善于分析",
                "沟通能力强，协调各方资源",
                "创新思维活跃，敢于尝试新想法"
            ]
        })
        
        # 创建UI设计师
        designer = TinyPerson("王小美")
        designer.define("age", 26)
        designer.define("occupation", {
            "title": "UI/UX设计师",
            "organization": "腾讯科技",
            "description": "负责移动应用界面设计和用户体验优化"
        })
        designer.define("personality", {
            "traits": [
                "审美能力强，追求视觉完美",
                "用户同理心强，理解用户痛点",
                "创意思维丰富，善于创新",
                "注重细节，精益求精"
            ]
        })
        
        self.agents = [developer, pm, designer]
        print(f"✅ 团队创建完成，共{len(self.agents)}人")
        
        return self.agents
    
    def setup_research_environment(self):
        """设置研究环境"""
        print("🏢 设置产品研究环境...")
        
        self.world = TinyWorld("产品研究会议室", self.agents)
        self.world.make_everyone_accessible()
        
        print("✅ 环境设置完成")
        return self.world
    
    def conduct_feature_brainstorming(self):
        """进行功能头脑风暴"""
        print("\n🧠 开始功能头脑风暴...")
        
        # 设置研究主题
        topic = """
        我们正在开发一个新的移动学习应用，目标用户是18-35岁的职场人士。
        应用主要功能包括：在线课程、学习计划、进度跟踪、社区讨论。
        请大家从各自的专业角度提出改进建议和新功能想法。
        """
        
        # 产品经理开始讨论
        pm = self.agents[1]  # 李雅
        pm.listen(f"作为产品经理，请分析这个学习应用的产品定位和核心功能：{topic}")
        
        # 运行讨论
        self.world.run(5)
        
        print("✅ 头脑风暴完成")
    
    def extract_insights(self):
        """提取洞察结果"""
        print("\n📊 提取研究洞察...")
        
        # 创建结果提取器
        extractor = ResultsExtractor()
        
        # 提取关键观点
        insights = extractor.extract_results_from_agents(
            self.agents,
            extraction_objective="提取关于移动学习应用的功能建议和改进意见",
            situation="产品研究会议讨论"
        )
        
        if insights:
            print("💡 提取到的关键洞察：")
            for i, insight in enumerate(insights, 1):
                print(f"{i}. {insight}")
        
        return insights
    
    def validate_agent_behavior(self):
        """验证智能体行为"""
        print("\n🔍 验证智能体行为...")
        
        validator = TinyPersonValidator()
        
        for agent in self.agents:
            # 验证智能体是否符合预期角色
            is_valid = validator.validate_person(
                agent,
                expectations=f"{agent.name}应该表现出{agent.get('occupation', {}).get('title', '专业人士')}的专业特征"
            )
            
            print(f"{'✅' if is_valid else '❌'} {agent.name} 行为验证: {'通过' if is_valid else '需要调整'}")

def market_research_simulation():
    """市场研究仿真"""
    print("\n📈 市场研究仿真示例")
    print("-" * 40)
    
    # 创建目标用户群体
    factory = TinyPersonFactory("中国一线城市的年轻职场人士")
    
    users = []
    user_profiles = [
        "25岁软件工程师，喜欢在线学习新技术",
        "28岁市场营销专员，希望提升职业技能",
        "32岁财务分析师，想要学习数据分析"
    ]
    
    for profile in user_profiles:
        user = factory.generate_person(profile)
        if user:
            users.append(user)
    
    if not users:
        print("❌ 用户生成失败")
        return
    
    # 创建市场研究环境
    market_world = TinyWorld("用户调研会", users)
    market_world.make_everyone_accessible()
    
    # 进行用户访谈
    research_questions = [
        "您在学习新技能时遇到的最大困难是什么？",
        "您希望学习应用具备哪些功能？",
        "您愿意为优质的在线学习内容付费吗？"
    ]
    
    for question in research_questions:
        print(f"\n❓ 研究问题: {question}")
        for user in users:
            user.listen_and_act(f"请回答这个问题：{question}")
        
        market_world.run(2)

def ab_testing_simulation():
    """A/B测试仿真"""
    print("\n🧪 A/B测试仿真示例")
    print("-" * 40)
    
    # 创建测试用户
    factory = TinyPersonFactory("移动应用用户")
    test_users = []
    
    for i in range(3):
        user = factory.generate_person(f"移动应用的活跃用户，年龄{20+i*5}岁")
        if user:
            test_users.append(user)
    
    if not test_users:
        print("❌ 测试用户生成失败")
        return
    
    # 定义两个版本的界面
    version_a = "版本A：传统的列表式课程展示，按分类排列"
    version_b = "版本B：卡片式课程展示，带有个性化推荐"
    
    # 进行A/B测试
    print(f"🅰️ 测试版本A: {version_a}")
    print(f"🅱️ 测试版本B: {version_b}")
    
    results = {"A": [], "B": []}
    
    for i, user in enumerate(test_users):
        version = "A" if i % 2 == 0 else "B"
        interface = version_a if version == "A" else version_b
        
        user.listen_and_act(f"请评价这个学习应用的界面设计：{interface}")
        
        # 收集反馈（这里简化处理）
        results[version].append(f"{user.name}的反馈")
    
    print("📊 A/B测试结果收集完成")

def main():
    """主函数"""
    print("🎭 TinyTroupe 高级使用示例")
    print("=" * 50)
    
    # 检查环境
    if not os.getenv('OPENAI_API_KEY') and not os.getenv('AZURE_OPENAI_KEY'):
        print("❌ 请设置API密钥环境变量")
        return
    
    try:
        # 1. 产品研究仿真
        print("\n🔬 1. 产品研究仿真")
        simulation = ProductResearchSimulation()
        simulation.create_research_team()
        simulation.setup_research_environment()
        simulation.conduct_feature_brainstorming()
        simulation.extract_insights()
        simulation.validate_agent_behavior()
        
        # 2. 市场研究仿真
        print("\n🔬 2. 市场研究仿真")
        market_research_simulation()
        
        # 3. A/B测试仿真
        print("\n🔬 3. A/B测试仿真")
        ab_testing_simulation()
        
        print("\n🎉 所有高级示例执行完成！")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
