#!/usr/bin/env python3
"""
TinyTroupe 基础使用示例
演示如何创建智能体、环境和进行基本交互
"""

import os
import sys

# 导入TinyTroupe核心模块
from tinytroupe.agent import TinyPerson
from tinytroupe.environment import TinyWorld
from tinytroupe.factory import TinyPersonFactory
from tinytroupe.examples import create_lisa_the_data_scientist, create_oscar_the_architect
import tinytroupe.control as control

def setup_environment():
    """设置基本环境和配置"""
    print("🚀 初始化TinyTroupe环境...")
    
    # 确保API密钥已设置
    if not os.getenv('OPENAI_API_KEY') and not os.getenv('AZURE_OPENAI_KEY'):
        print("❌ 请设置 OPENAI_API_KEY 或 AZURE_OPENAI_KEY 环境变量")
        return False
    
    print("✅ API密钥配置完成")
    return True

def create_custom_agent():
    """创建自定义智能体"""
    print("\n👤 创建自定义智能体...")
    
    # 方法1: 程序化创建
    alice = TinyPerson("Alice")
    
    # 定义基本信息
    alice.define("age", 25)
    alice.define("nationality", "Chinese")
    alice.define("occupation", {
        "title": "产品经理",
        "organization": "科技公司",
        "description": "负责移动应用产品设计和用户体验优化"
    })
    
    # 定义性格特征
    alice.define("personality", {
        "traits": [
            "善于沟通，喜欢团队合作",
            "注重细节，追求完美",
            "对新技术充满好奇心",
            "有时会因为工作压力而焦虑"
        ]
    })
    
    # 定义兴趣爱好
    alice.define("preferences", {
        "interests": [
            "用户体验设计",
            "移动应用开发",
            "数据分析",
            "瑜伽和冥想",
            "旅行摄影"
        ]
    })
    
    print(f"✅ 创建智能体: {alice.name}")
    return alice

def create_agent_from_factory():
    """使用工厂模式创建智能体"""
    print("\n🏭 使用工厂创建智能体...")
    
    # 创建工厂实例
    factory = TinyPersonFactory("一家专注于人工智能的科技公司")
    
    # 生成智能体
    bob = factory.generate_person(
        "创建一个中国的AI工程师，喜欢开源项目和技术分享，性格内向但专业能力强"
    )
    
    if bob:
        print(f"✅ 工厂生成智能体: {bob.name}")
        return bob
    else:
        print("❌ 智能体生成失败")
        return None

def simple_conversation():
    """简单对话示例"""
    print("\n💬 开始简单对话...")
    
    # 使用预定义的智能体
    lisa = create_lisa_the_data_scientist()
    oscar = create_oscar_the_architect()
    
    # 创建环境
    world = TinyWorld("办公室聊天室", [lisa, oscar])
    world.make_everyone_accessible()
    
    # 开始对话
    print("\n--- 对话开始 ---")
    lisa.listen("向Oscar介绍一下你自己，并询问他的工作")
    
    # 运行几轮对话
    world.run(3)
    print("--- 对话结束 ---")

def agent_with_tools():
    """智能体使用工具示例"""
    print("\n🔧 智能体工具使用示例...")
    
    alice = create_custom_agent()
    
    # 让智能体执行任务
    alice.listen_and_act("请制定一个产品功能优化计划")
    
    print("✅ 任务执行完成")

def simulation_with_caching():
    """带缓存的仿真示例"""
    print("\n💾 缓存仿真示例...")
    
    # 开始缓存会话
    control.begin("simulation_cache.json")
    
    try:
        # 创建智能体
        alice = create_custom_agent()
        bob = create_agent_from_factory()
        
        if bob:
            # 创建环境
            world = TinyWorld("产品讨论会", [alice, bob])
            world.make_everyone_accessible()
            
            # 设置检查点
            control.checkpoint()
            
            # 进行讨论
            alice.listen("我们来讨论一下如何改进我们的移动应用用户体验")
            world.run(2)
            
    finally:
        # 结束缓存会话
        control.end()
    
    print("✅ 仿真完成，状态已缓存")

def main():
    """主函数"""
    print("🎭 TinyTroupe 基础使用示例")
    print("=" * 50)
    
    # 环境设置
    if not setup_environment():
        return
    
    try:
        # 1. 创建自定义智能体
        create_custom_agent()
        
        # 2. 使用工厂创建智能体
        create_agent_from_factory()
        
        # 3. 简单对话
        simple_conversation()
        
        # 4. 智能体工具使用
        agent_with_tools()
        
        # 5. 缓存仿真
        simulation_with_caching()
        
        print("\n🎉 所有示例执行完成！")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        print("请检查API密钥配置和网络连接")

if __name__ == "__main__":
    main()
