[OpenAI]
# API类型选择: openai 或 azure
API_TYPE=openai

# Azure OpenAI 配置 (如果使用Azure)
AZURE_API_VERSION=2024-08-01-preview

# 模型参数配置
MODEL=gpt-4o-mini              # 推荐使用gpt-4o-mini (成本较低)
MAX_TOKENS=4000                # 最大token数
TEMPERATURE=1.2                # 创造性参数 (0.0-2.0)
FREQ_PENALTY=0.0               # 频率惩罚
PRESENCE_PENALTY=0.0           # 存在惩罚
TIMEOUT=60                     # 请求超时时间
MAX_ATTEMPTS=5                 # 最大重试次数
WAITING_TIME=2                 # 重试等待时间
EXPONENTIAL_BACKOFF_FACTOR=5   # 指数退避因子

# 嵌入模型配置
EMBEDDING_MODEL=text-embedding-3-small
AZURE_EMBEDDING_MODEL_API_VERSION=2023-05-15

# 缓存配置
CACHE_API_CALLS=True           # 启用API调用缓存 (节省成本)
CACHE_FILE_NAME=openai_api_cache.pickle

# 显示配置
MAX_CONTENT_DISPLAY_LENGTH=1024

[Simulation]
# 安全配置
RAI_HARMFUL_CONTENT_PREVENTION=True
RAI_COPYRIGHT_INFRINGEMENT_PREVENTION=True

[Logging]
# 日志级别: ERROR, WARNING, INFO, DEBUG
LOGLEVEL=INFO
